/* App Details Component Styling */
.app-details {
  position: relative;
  min-height: 400px;
  animation: appDetailsSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Smooth slide-in animation with native Android-like feel */
@keyframes appDetailsSlideIn {
  0% {
    opacity: 0;
    transform: translateX(100%) scale(0.85);
  }
  60% {
    opacity: 0.8;
    transform: translateX(-5%) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Header */
.app-details__header {
  margin-bottom: 24px;
}

.app-details__back-btn {
  background: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.app-details__back-btn:hover {
  background: var(--card-bg);
  color: var(--text-primary);
  border-color: var(--text-secondary);
  transform: translateX(-2px);
}

/* Main Content */
.app-details__content {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

/* App Icon */
.app-details__icon {
  position: relative;
  flex-shrink: 0;
  width: 120px;
  height: 120px;
  padding: 10px;
  border-radius: 20px;
  background: var(--card-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid var(--border-color);
  overflow: hidden;
  animation: iconFloat 0.6s ease-out 0.2s both;
}

@keyframes iconFloat {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.app-details__icon-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 16px;
}

.app-details__icon-fallback {
  display: none;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--text-secondary);
}

/* App Information */
.app-details__info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  animation: infoSlideIn 0.6s ease-out 0.3s both;
}

@keyframes infoSlideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* App Name */
.app-details__name {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
  text-align: justify;
}

/* Version and Category */
.app-details__version,
.app-details__category {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.app-details__version-label,
.app-details__category-label {
  color: var(--text-secondary);
  font-weight: 500;
  min-width: 60px;
}

.app-details__version-value {
  background: var(--accent-color);
  color: var(--primary-bg);
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 12px;
}

.app-details__category-value {
  background: var(--card-bg);
  color: var(--text-primary);
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 12px;
  border: 1px solid var(--border-color);
  text-transform: capitalize;
}

/* Description */
.app-details__description {
  margin-top: 8px;
}

.app-details__description-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  text-align: justify;
}

.app-details__description-text {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
  font-size: 14px;
  white-space: pre-wrap;
  transition: all 0.3s ease;
  text-align: justify;
  margin-bottom: 0 !important;
}

.app-details__description-toggle {
  background: none;
  border: none;
  color: var(--accent-color);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  padding: 4px 0;
  transition: all 0.2s ease;
  display: flex;
}

.app-details__description-toggle:hover {
  color: var(--accent-hover);
  text-decoration: underline;
}

/* Actions */
.app-details__actions {
  margin-top: 12px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  animation: actionsSlideUp 0.6s ease-out 0.4s both;
}

@keyframes actionsSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.app-details__open-btn {
  background: var(--accent-color);
  color: var(--primary-bg);
  border: none;
  padding: 12px 24px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.app-details__open-btn:hover:not(:disabled) {
  background: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.app-details__open-btn:active {
  transform: translateY(0);
}

.app-details__open-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Add to My Apps Button */
.app-details__add-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.app-details__add-btn:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.app-details__add-btn:active {
  transform: translateY(0);
}

.app-details__add-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  background: var(--text-secondary);
}

/* Remove Button */
.app-details__remove-btn {
  background: transparent;
  color: var(--error-color, #dc3545);
  border: 1px solid var(--error-color, #dc3545);
  padding: 12px 24px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.app-details__remove-btn:hover {
  background: var(--error-color, #dc3545);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(220, 53, 69, 0.2);
}

.app-details__remove-btn:active {
  transform: translateY(0);
}

/* External Link Button */
.app-details__external-btn {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  padding: 12px 24px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.app-details__external-btn:hover {
  background: var(--card-bg);
  color: var(--text-primary);
  border-color: var(--text-secondary);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.app-details__external-btn:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-details__content {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 20px;
  }

  .app-details__icon {
    width: 100px;
    height: 100px;
  }

  .app-details__name {
    font-size: 24px;
  }

  .app-details__info {
    width: 100%;
  }

  .app-details__version,
  .app-details__category {
    justify-content: center;
  }

  .app-details__description {
    text-align: left;
  }

  .app-details__actions {
    width: 100%;
    flex-direction: column;
    gap: 8px;
  }

  .app-details__open-btn,
  .app-details__add-btn,
  .app-details__remove-btn,
  .app-details__external-btn {
    width: 100%;
    justify-content: center;
  }
}
